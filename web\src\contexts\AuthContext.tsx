import React, { createContext, useContext, useState, useEffect, type ReactNode } from 'react';
import { apiService, type User } from '../services/api';
import { useCurrentUserData } from '../hooks/useCurrentUserData';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<{
    success: boolean;
    message?: string;
    requiresEmailVerification?: boolean;
    email?: string;
  }>;
  logout: () => Promise<void>;
  setCurrentUser: (user: User | null) => void;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const { user, setCurrentUser } = useCurrentUserData();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if user is already authenticated on app start
    const checkAuthStatus = async () => {
      try {
        if (apiService.isAuthenticated()) {
          // Validate the token by fetching current user data from server
          const response = await apiService.getProfile();

          if (response.success && response.data?.user) {
            // Set the real user data from the server
            setCurrentUser(response.data.user);
          } else {
            // Token is invalid or expired, clear it
            console.warn('Token validation failed:', response.message);
            await apiService.logout();
            setCurrentUser(null);
          }
        } else {
          // No token found, user is not authenticated
          setCurrentUser(null);
        }
      } catch (error) {
        console.error('Auth check failed:', error);
        // If there's an error (like network issue or invalid token), clear auth state
        try {
          await apiService.logout();
        } catch (logoutError) {
          console.error('Logout error during auth check:', logoutError);
        }
        setCurrentUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthStatus();
  }, []);

  const login = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      const response = await apiService.login({ email: email.trim().toLowerCase(), password });

      if (response.success && response.data) {
        setCurrentUser(response.data.user);
        return { success: true };
      } else {
        return {
          success: false,
          message: response.message || 'Login failed',
          requiresEmailVerification: (response as any).requiresEmailVerification,
          email: (response as any).email
        };
      }
    } catch (error) {
      return {
        success: false,
        message: 'An unexpected error occurred'
      };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      await apiService.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setCurrentUser(null);
    }
  };

  const refreshUser = async () => {
    try {
      if (apiService.isAuthenticated()) {
        const response = await apiService.getProfile();

        if (response.success && response.data?.user) {
          setCurrentUser(response.data.user);
        } else {
          // Token is invalid, logout user
          await logout();
        }
      }
    } catch (error) {
      console.error('Refresh user error:', error);
      // If there's an error, logout user
      await logout();
    }
  };

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    logout,
    setCurrentUser,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
